// Get packing lists by invoice ID
getPackingListsByInvoiceId(invoiceId: string): Observable<any[]> {
  return this.http.get<any[]>(`${this.apiUrl}/exportPackingList/exportPacking/byInvoice/${invoiceId}`).pipe(
    map(response => {
      // Handle different response formats
      if (Array.isArray(response)) {
        return response;
      } else if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      } else {
        return [];
      }
    }),
    catchError(error => {
      console.error('Error fetching packing lists by invoice ID:', error);
      return of([]);
    })
  );
}

// Save shipping details
saveShippingDetails(invoiceId: string, shippingDetails: any): Observable<any> {
  return this.http.patch(`${this.apiUrl}/exportInvoice/${invoiceId}/shipping`, shippingDetails);
}