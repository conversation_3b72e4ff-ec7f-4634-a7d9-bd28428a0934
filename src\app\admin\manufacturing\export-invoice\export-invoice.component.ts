import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
// Import specific Angular Material modules instead of MaterialModules
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { HttpClientModule, HttpClient } from '@angular/common/http';
import { ExportInvoiceService, InvoiceData } from './export-invoice.service';
import { Observable, of, catchError, map } from 'rxjs';
import { MasterService } from '../../../services/master.service';
import { Router } from '@angular/router';
import { SweetalertService } from '../../../services/sweetalert.service';

interface InvoiceListItem {
  id: string;
  number: string;
  date: string;
}

@Component({
  selector: 'app-invoice-form',
  templateUrl: './export-invoice.component.html',
  styleUrls: ['./export-invoice.component.css'],
  // Added imports for FormsModule to support ngModel and CommonModule for *ngIf
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    HttpClientModule,
    MatInputModule,
    MatTableModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatAutocompleteModule
  ],
  standalone: true
})
export class ExportInvoiceComponent implements OnInit {
  invoiceForm: FormGroup;
  totalPieces: number = 0;
  totalQuantity: number = 0;
  totalCifEuro: string = '0.00';
  totalFOB: string = '0.00';
  grandTotal: string = '0.00';
  signatureImageUrl: string | null = null;
  signatureBase64: string | null = null; // Store base64 for database

  // Invoice selection properties
  showInvoiceForm: boolean = false;
  showOldInvoices: boolean = false;
  selectedOldInvoice: string = '';
  oldInvoices: InvoiceListItem[] = [];
  isLoading: boolean = false;
  isSubmitting: boolean = false;

  // Buyer order and consignee properties
  buyerOrders: any[] = [];
  filteredBuyerOrders: any[] = [];
  buyersOrderNoRefs: string[] = [];
  consignees: any[] = [];
  selectedBuyerOrderId: string = '';
  selectedConsigneeId: string = '';
  buyers: any[] = [];
  selectedBuyer: any = null;
  buyersListText: string = '';

  // Packing list properties
  packingLists: any[] = [];

  // Country and port options
  countries = [
    { value: 'GERMANY', viewValue: 'Germany' },
    { value: 'FRANCE', viewValue: 'France' },
    { value: 'NETHERLANDS', viewValue: 'Netherlands' },
    { value: 'UNITED KINGDOM', viewValue: 'United Kingdom' },
    { value: 'ITALY', viewValue: 'Italy' },
    { value: 'SPAIN', viewValue: 'Spain' },
    { value: 'BELGIUM', viewValue: 'Belgium' },
    { value: 'SWITZERLAND', viewValue: 'Switzerland' },
    { value: 'AUSTRIA', viewValue: 'Austria' },
    { value: 'DENMARK', viewValue: 'Denmark' },
    { value: 'SWEDEN', viewValue: 'Sweden' },
    { value: 'NORWAY', viewValue: 'Norway' },
    { value: 'FINLAND', viewValue: 'Finland' },
    { value: 'PORTUGAL', viewValue: 'Portugal' },
    { value: 'GREECE', viewValue: 'Greece' },
    { value: 'IRELAND', viewValue: 'Ireland' },
    { value: 'POLAND', viewValue: 'Poland' },
    { value: 'CZECH REPUBLIC', viewValue: 'Czech Republic' },
    { value: 'HUNGARY', viewValue: 'Hungary' },
    { value: 'UNITED STATES', viewValue: 'United States' },
    { value: 'CANADA', viewValue: 'Canada' },
    { value: 'AUSTRALIA', viewValue: 'Australia' },
    { value: 'NEW ZEALAND', viewValue: 'New Zealand' },
    { value: 'JAPAN', viewValue: 'Japan' },
    { value: 'CHINA', viewValue: 'China' },
    { value: 'SOUTH KOREA', viewValue: 'South Korea' },
    { value: 'SINGAPORE', viewValue: 'Singapore' },
    { value: 'UNITED ARAB EMIRATES', viewValue: 'United Arab Emirates' },
    { value: 'SAUDI ARABIA', viewValue: 'Saudi Arabia' },
    { value: 'QATAR', viewValue: 'Qatar' },
    { value: 'KUWAIT', viewValue: 'Kuwait' },
    { value: 'BAHRAIN', viewValue: 'Bahrain' },
    { value: 'OMAN', viewValue: 'Oman' }
  ];

  indianPorts = [
    { value: 'MUMBAI', viewValue: 'Mumbai' },
    { value: 'NHAVA SHEVA', viewValue: 'Nhava Sheva' },
    { value: 'CHENNAI', viewValue: 'Chennai' },
    { value: 'KOLKATA', viewValue: 'Kolkata' },
    { value: 'COCHIN', viewValue: 'Cochin' },
    { value: 'TUTICORIN', viewValue: 'Tuticorin' },
    { value: 'VIZAG', viewValue: 'Visakhapatnam' },
    { value: 'KANDLA', viewValue: 'Kandla' },
    { value: 'MUNDRA', viewValue: 'Mundra' },
    { value: 'PIPAVAV', viewValue: 'Pipavav' },
    { value: 'HAZIRA', viewValue: 'Hazira' },
    { value: 'MANGALORE', viewValue: 'Mangalore' },
    { value: 'PARADIP', viewValue: 'Paradip' },
    { value: 'ENNORE', viewValue: 'Ennore' },
    { value: 'KRISHNAPATNAM', viewValue: 'Krishnapatnam' },
    { value: 'KAKINADA', viewValue: 'Kakinada' },
    { value: 'DAHEJ', viewValue: 'Dahej' },
    { value: 'DHAMRA', viewValue: 'Dhamra' },
    { value: 'HALDIA', viewValue: 'Haldia' },
    { value: 'JNPT', viewValue: 'Jawaharlal Nehru Port' }
  ];
  packingListApiUrl: any;

  exporterRefs: string[] = [];
  consigneeRefs: string[] = [];
  otherReferencesRefs: string[] = [];
  buyerIfOtherRefs: string[] = [];
  preCarriageByRefs: string[] = [];
  placeOfReceiptRefs: string[] = [];
  vesselNoRefs: string[] = [];
  portOfDischargeRefs: string[] = [];
  descriptionOfGoodsRefs: string[] = [];
  marksAndNosRefs: string[] = [];
  noOfKindOfPackageRefs: string[] = [];

  isUpdateMode: boolean = false;
  loadedInvoiceId: string | null = null;

  exporterText: string = '';

  // For signature upload
  onSignatureSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        this.snackBar.open('Please select an image file', 'Close', {
          duration: 3000
        });
        return;
      }

      // Check file size (10KB to 100KB)
      const minSize = 10 * 1024; // 10KB in bytes
      const maxSize = 100 * 1024; // 100KB in bytes

      if (file.size < minSize) {
        this.snackBar.open('Signature image is too small. Minimum size is 10KB', 'Close', {
          duration: 3000
        });
        return;
      }

      if (file.size > maxSize) {
        this.snackBar.open('Signature image is too large. Maximum size is 100KB', 'Close', {
          duration: 3000
        });
        return;
      }

      // Create a URL for preview
      this.signatureImageUrl = URL.createObjectURL(file);

      // Convert to base64 for database storage
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.signatureBase64 = e.target.result; // This includes data:image/jpeg;base64,
        console.log('✅ Signature converted to base64 for database storage');
      };
      reader.readAsDataURL(file);
    }
  }

  // Remove signature
  removeSignature(): void {
    this.signatureImageUrl = null;
    this.signatureBase64 = null;
  }

  // Helper method to load buyers (Promise version)
  private loadBuyersPromise(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.masterService.getAllBuyerList().subscribe({
        next: (buyers: any[]) => {
          this.buyers = buyers;
          console.log('✅ Buyers loaded:', buyers.length);
          resolve();
        },
        error: (error: any) => {
          console.error('❌ Error loading buyers:', error);
          reject(error);
        }
      });
    });
  }

  // Helper method to match and set consignee
  private matchAndSetConsignee(consigneeString: string): void {
    console.log('🔍 Matching consignee:', consigneeString);

    // Parse consignee string to find matching buyer
    const consigneeParts = consigneeString.split(',');
    const consigneeName = consigneeParts[0]?.trim();

    console.log('🔍 Looking for buyer matching:', consigneeName);

    // Find matching buyer by name or customer code
    const matchingBuyer = this.buyers.find(buyer => {
      const nameMatch = buyer.customerName?.toLowerCase().includes(consigneeName?.toLowerCase()) ||
                       consigneeName?.toLowerCase().includes(buyer.customerName?.toLowerCase());
      const codeMatch = buyer.customerCode?.toLowerCase().includes(consigneeName?.toLowerCase()) ||
                       consigneeName?.toLowerCase().includes(buyer.customerCode?.toLowerCase());

      return nameMatch || codeMatch;
    });

    if (matchingBuyer) {
      this.selectedBuyer = matchingBuyer;
      this.selectedConsigneeId = matchingBuyer._id;
      console.log('✅ Found matching buyer:', matchingBuyer.customerName);

      // Update the consignee field with proper format
      const consigneeText = `${matchingBuyer.customerName},\n${matchingBuyer.customerAddress},\n${matchingBuyer.country} ${matchingBuyer.zipCode}`;
      this.invoiceForm.patchValue({
        consignee: consigneeText
      });
    } else {
      console.log('❌ No matching buyer found for:', consigneeName);
    }
  }

  /**
   * Force uppercase for all text input fields as user types.
   * Usage: (input)="toUppercaseInput($event, 'formControlName')"
   * @param event Input event
   * @param controlName Name of the FormControl
   */
  toUppercaseInput(event: any, controlName: string): void {
    const value = event.target.value?.toUpperCase() || '';
    this.invoiceForm.get(controlName)?.setValue(value, { emitEvent: false });
    // Also update the input's value to avoid cursor jump
    event.target.value = value;
  }

  /**
   * Force uppercase for mat-select fields when an option is selected.
   * Usage: (selectionChange)="toUppercaseSelect($event.value, 'formControlName')"
   * @param value The selected value
   * @param controlName Name of the FormControl
   */
  toUppercaseSelect(value: string, controlName: string): void {
    if (typeof value === 'string') {
      const upperValue = value.toUpperCase();
      this.invoiceForm.get(controlName)?.setValue(upperValue);
    } else {
      this.invoiceForm.get(controlName)?.setValue(value);
    }
  }

  constructor(
    private fb: FormBuilder,
    private invoiceService: ExportInvoiceService,
    private snackBar: MatSnackBar,
    private http: HttpClient,
    private masterService: MasterService, // Inject MasterService
    private router: Router, // Inject Router
    private sweetalertService: SweetalertService // Inject SweetalertService
  ) {
    this.invoiceForm = this.fb.group({
      invoiceNumber: ['RE-755', Validators.required],
      invoiceDate: [new Date(), Validators.required],
      exporterRef: ['DEFAULT-REF'], // Made optional with default
      exporter: ['M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA', Validators.required],
      buyersOrderNo: ['DEFAULT-ORDER'], // Made optional with default
      buyersOrderDate: [new Date()], // Made optional with default date
      otherReferences: [''],
      consignee: ['DEFAULT CONSIGNEE\nDefault Address\nGermany'], // Made optional with default
      buyerIfOther: ['SAME'],
      preCarriageBy: [''],
      placeOfReceipt: [''],
      originCountry: ['INDIA'],
      destinationCountry: ['Germany'], // Added default
      vesselNo: [''],
      portOfLoading: [''],
      portOfDischarge: ['Hamburg'], // Added default
      finalDestination: ['Germany'], // Added default
      marksAndNos: [''],
      noOfKindOfPackage: [''],
      descriptionOfGoods: ['Indian Hand-Knotted Woolen Carpets'],
      area: ['507f1f77bcf86cd799439011'], // Default ObjectId for area
      goods: this.fb.array([
        this.createGoodsItem('Default Quality', 'Default Design', 1, 1, 100), // Start with default values
      ]),
      addedFreight: ['0.00'],
      insurance: ['0.00'],
      igstPercentage: ['0.00'],
      igst: ['0.00'],
      amountChargeableWords: ['One Hundred Only'], // Added default

      signature: [''] // Use FormControl for signature, not an object
    });

    this.goods.valueChanges.subscribe(() => this.calculateTotals());
    this.invoiceForm.get('addedFreight')?.valueChanges.subscribe(() => this.calculateTotals());
    this.calculateTotals(); // Initial calculation
  }

  createGoodsItem(quality: string = 'Default Quality', design: string = 'Default Design', pieces: number = 1, quantitySqMeter: number = 1, rateFOB: number = 100): FormGroup {
    return this.fb.group({
      quality: [quality], // Made optional
      design: [design], // Made optional
      pieces: [pieces || 1], // Default to 1
      quantitySqMeter: [quantitySqMeter || 1], // Default to 1
      rateFOB: [rateFOB || 100], // Default to 100
      amountFOB: [{ value: ((quantitySqMeter || 1) * (rateFOB || 100)).toFixed(2), disabled: true }],
    });
  }

  // Calculate area based on length, width and unit
  calculateArea(length: number, width: number, unit: string): number {
    const area = length * width;
    // If unit is sqyd, convert from sqft to sqyd (1 sqyd = 9 sqft)
    return unit === 'sqyd' ? area / 9 : area;
  }

  // Debug form validation
  debugFormValidation(): void {
    console.log('=== FORM VALIDATION DEBUG ===');
    console.log('Form valid:', this.invoiceForm.valid);
    console.log('Form errors:', this.invoiceForm.errors);

    Object.keys(this.invoiceForm.controls).forEach(key => {
      const control = this.invoiceForm.get(key);
      if (control && control.invalid) {
        console.log(`Field '${key}' is invalid:`, control.errors);
        console.log(`Field '${key}' value:`, control.value);
      }
    });

    // Check goods array
    const goodsArray = this.invoiceForm.get('goods') as FormArray;
    if (goodsArray && goodsArray.invalid) {
      console.log('Goods array is invalid');
      goodsArray.controls.forEach((control, index) => {
        if (control.invalid) {
          console.log(`Goods item ${index} is invalid:`, control.errors);
          console.log(`Goods item ${index} value:`, control.value);
        }
      });
    }
  }

  ngOnInit(): void {
    // Load Exporter's Ref options from localStorage if available
    const savedRefs = localStorage.getItem('exporterRefs');
    if (savedRefs) {
      this.exporterRefs = JSON.parse(savedRefs);
    } else {
      this.exporterRefs = [];
    }

    // Load Consignee options
    const savedConsigneeRefs = localStorage.getItem('consigneeRefs');
    this.consigneeRefs = savedConsigneeRefs ? JSON.parse(savedConsigneeRefs) : [];
    // Load Other Reference(s) options
    const savedOtherReferencesRefs = localStorage.getItem('otherReferencesRefs');
    this.otherReferencesRefs = savedOtherReferencesRefs ? JSON.parse(savedOtherReferencesRefs) : [];
    // Load Buyer (if other than consignee) options
    const savedBuyerIfOtherRefs = localStorage.getItem('buyerIfOtherRefs');
    this.buyerIfOtherRefs = savedBuyerIfOtherRefs ? JSON.parse(savedBuyerIfOtherRefs) : [];
    // Load Pre-Carriage By options
    const savedPreCarriageByRefs = localStorage.getItem('preCarriageByRefs');
    this.preCarriageByRefs = savedPreCarriageByRefs ? JSON.parse(savedPreCarriageByRefs) : [];
    // Load Place of Receipt options
    const savedPlaceOfReceiptRefs = localStorage.getItem('placeOfReceiptRefs');
    this.placeOfReceiptRefs = savedPlaceOfReceiptRefs ? JSON.parse(savedPlaceOfReceiptRefs) : [];
    // Load Vessel No options
    const savedVesselNoRefs = localStorage.getItem('vesselNoRefs');
    this.vesselNoRefs = savedVesselNoRefs ? JSON.parse(savedVesselNoRefs) : [];
    // Load Port of Discharge options
    const savedPortOfDischargeRefs = localStorage.getItem('portOfDischargeRefs');
    this.portOfDischargeRefs = savedPortOfDischargeRefs ? JSON.parse(savedPortOfDischargeRefs) : [];
    // Load Description of Goods options
    const savedDescriptionOfGoodsRefs = localStorage.getItem('descriptionOfGoodsRefs');
    this.descriptionOfGoodsRefs = savedDescriptionOfGoodsRefs ? JSON.parse(savedDescriptionOfGoodsRefs) : [];
    // Load Marks & Cont No. options
    const savedMarksAndNosRefs = localStorage.getItem('marksAndNosRefs');
    this.marksAndNosRefs = savedMarksAndNosRefs ? JSON.parse(savedMarksAndNosRefs) : [];
    // Load No & Kind of Package options
    const savedNoOfKindOfPackageRefs = localStorage.getItem('noOfKindOfPackageRefs');
    this.noOfKindOfPackageRefs = savedNoOfKindOfPackageRefs ? JSON.parse(savedNoOfKindOfPackageRefs) : [];
    // Load Buyer's Order No. options
    const savedBuyersOrderNoRefs = localStorage.getItem('buyersOrderNoRefs');
    this.buyersOrderNoRefs = savedBuyersOrderNoRefs ? JSON.parse(savedBuyersOrderNoRefs) : [];

    // Load existing invoices when component initializes
    this.loadInvoices();

    // Load buyer orders
    this.loadBuyerOrders();

    // Load consignees and buyers together (they're the same data)
    this.loadBuyers();

    // Fetch areas (SizeMaster) for dropdown
    this.masterService.getAllSizesList().subscribe({
      next: (sizes: any) => {
        if (Array.isArray(sizes)) {
          this.areas = sizes;
        } else if (sizes && Array.isArray(sizes.data)) {
          this.areas = sizes.data;
        } else {
          this.areas = [];
        }
      },
      error: (err) => {
        this.areas = [];
      }
    });
  }

  private saveRefs(key: string, arr: string[]) {
    localStorage.setItem(key, JSON.stringify(arr));
  }

  onExporterRefBlur() {
    const value = this.invoiceForm.get('exporterRef')?.value;
    if (value && !this.exporterRefs.includes(value)) {
      this.exporterRefs.push(value);
      this.saveRefs('exporterRefs', this.exporterRefs);
    }
  }

  removeExporterRef(ref: string) {
    const index = this.exporterRefs.indexOf(ref);
    if (index > -1) {
      this.exporterRefs.splice(index, 1);
      this.saveRefs('exporterRefs', this.exporterRefs);
      // If the removed ref is currently selected, clear the field
      if (this.invoiceForm.get('exporterRef')?.value === ref) {
        this.invoiceForm.get('exporterRef')?.setValue('');
      }
    }
  }

  onConsigneeBlur() {
    const value = this.invoiceForm.get('consignee')?.value;
    if (value && !this.consigneeRefs.includes(value)) {
      this.consigneeRefs.push(value);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
    }
  }

  removeConsigneeRef(ref: string) {
    const index = this.consigneeRefs.indexOf(ref);
    if (index > -1) {
      this.consigneeRefs.splice(index, 1);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
      // If the removed ref is currently selected, clear the field
      if (this.invoiceForm.get('consignee')?.value === ref) {
        this.invoiceForm.get('consignee')?.setValue('');
      }
    }
  }

  onConsigneeOptionSelected(event: any) {
    // This method handles when an option is selected from autocomplete
    // The value is already set by the autocomplete, so we just need to save it
    this.onConsigneeBlur();
  }

  onBuyerSelected(buyer: any) {
    // When a buyer is selected from the buyer master dropdown
    this.selectedBuyer = buyer;
    const consigneeText = `${buyer.customerName},\n${buyer.customerAddress},\n${buyer.country} ${buyer.zipCode}`;
    this.invoiceForm.patchValue({
      consignee: consigneeText
    });
    // Save to consignee refs if not already present
    if (!this.consigneeRefs.includes(consigneeText)) {
      this.consigneeRefs.push(consigneeText);
      this.saveRefs('consigneeRefs', this.consigneeRefs);
    }
  }



  onOtherReferencesBlur() {
    const value = this.invoiceForm.get('otherReferences')?.value;
    if (value && !this.otherReferencesRefs.includes(value)) {
      this.otherReferencesRefs.push(value);
      this.saveRefs('otherReferencesRefs', this.otherReferencesRefs);
    }
  }

  removeOtherReferencesRef(ref: string) {
    const idx = this.otherReferencesRefs.indexOf(ref);
    if (idx > -1) {
      this.otherReferencesRefs.splice(idx, 1);
      this.saveRefs('otherReferencesRefs', this.otherReferencesRefs);
      if (this.invoiceForm.get('otherReferences')?.value === ref) {
        this.invoiceForm.get('otherReferences')?.setValue('');
      }
    }
  }

  onBuyerIfOtherBlur() {
    const value = this.invoiceForm.get('buyerIfOther')?.value;
    if (value && !this.buyerIfOtherRefs.includes(value)) {
      this.buyerIfOtherRefs.push(value);
      this.saveRefs('buyerIfOtherRefs', this.buyerIfOtherRefs);
    }
  }

  removeBuyerIfOtherRef(ref: string) {
    const idx = this.buyerIfOtherRefs.indexOf(ref);
    if (idx > -1) {
      this.buyerIfOtherRefs.splice(idx, 1);
      this.saveRefs('buyerIfOtherRefs', this.buyerIfOtherRefs);
      if (this.invoiceForm.get('buyerIfOther')?.value === ref) {
        this.invoiceForm.get('buyerIfOther')?.setValue('');
      }
    }
  }

  onPreCarriageByBlur() {
    const value = this.invoiceForm.get('preCarriageBy')?.value;
    if (value && !this.preCarriageByRefs.includes(value)) {
      this.preCarriageByRefs.push(value);
      this.saveRefs('preCarriageByRefs', this.preCarriageByRefs);
    }
  }

  removePreCarriageByRef(ref: string) {
    const idx = this.preCarriageByRefs.indexOf(ref);
    if (idx > -1) {
      this.preCarriageByRefs.splice(idx, 1);
      this.saveRefs('preCarriageByRefs', this.preCarriageByRefs);
      if (this.invoiceForm.get('preCarriageBy')?.value === ref) {
        this.invoiceForm.get('preCarriageBy')?.setValue('');
      }
    }
  }

  onPlaceOfReceiptBlur() {
    const value = this.invoiceForm.get('placeOfReceipt')?.value;
    if (value && !this.placeOfReceiptRefs.includes(value)) {
      this.placeOfReceiptRefs.push(value);
      this.saveRefs('placeOfReceiptRefs', this.placeOfReceiptRefs);
    }
  }

  removePlaceOfReceiptRef(ref: string) {
    const idx = this.placeOfReceiptRefs.indexOf(ref);
    if (idx > -1) {
      this.placeOfReceiptRefs.splice(idx, 1);
      this.saveRefs('placeOfReceiptRefs', this.placeOfReceiptRefs);
      if (this.invoiceForm.get('placeOfReceipt')?.value === ref) {
        this.invoiceForm.get('placeOfReceipt')?.setValue('');
      }
    }
  }

  onVesselNoBlur() {
    const value = this.invoiceForm.get('vesselNo')?.value;
    if (value && !this.vesselNoRefs.includes(value)) {
      this.vesselNoRefs.push(value);
      this.saveRefs('vesselNoRefs', this.vesselNoRefs);
    }
  }

  removeVesselNoRef(ref: string) {
    const idx = this.vesselNoRefs.indexOf(ref);
    if (idx > -1) {
      this.vesselNoRefs.splice(idx, 1);
      this.saveRefs('vesselNoRefs', this.vesselNoRefs);
      if (this.invoiceForm.get('vesselNo')?.value === ref) {
        this.invoiceForm.get('vesselNo')?.setValue('');
      }
    }
  }

  onPortOfDischargeBlur() {
    const value = this.invoiceForm.get('portOfDischarge')?.value;
    if (value && !this.portOfDischargeRefs.includes(value)) {
      this.portOfDischargeRefs.push(value);
      this.saveRefs('portOfDischargeRefs', this.portOfDischargeRefs);
    }
  }

  removePortOfDischargeRef(ref: string) {
    const idx = this.portOfDischargeRefs.indexOf(ref);
    if (idx > -1) {
      this.portOfDischargeRefs.splice(idx, 1);
      this.saveRefs('portOfDischargeRefs', this.portOfDischargeRefs);
      if (this.invoiceForm.get('portOfDischarge')?.value === ref) {
        this.invoiceForm.get('portOfDischarge')?.setValue('');
      }
    }
  }

  onDescriptionOfGoodsBlur() {
    const value = this.invoiceForm.get('descriptionOfGoods')?.value;
    if (value && !this.descriptionOfGoodsRefs.includes(value)) {
      this.descriptionOfGoodsRefs.push(value);
      this.saveRefs('descriptionOfGoodsRefs', this.descriptionOfGoodsRefs);
    }
  }

  removeDescriptionOfGoodsRef(ref: string) {
    const idx = this.descriptionOfGoodsRefs.indexOf(ref);
    if (idx > -1) {
      this.descriptionOfGoodsRefs.splice(idx, 1);
      this.saveRefs('descriptionOfGoodsRefs', this.descriptionOfGoodsRefs);
      if (this.invoiceForm.get('descriptionOfGoods')?.value === ref) {
        this.invoiceForm.get('descriptionOfGoods')?.setValue('');
      }
    }
  }

  onMarksAndNosBlur() {
    const value = this.invoiceForm.get('marksAndNos')?.value;
    if (value && !this.marksAndNosRefs.includes(value)) {
      this.marksAndNosRefs.push(value);
      this.saveRefs('marksAndNosRefs', this.marksAndNosRefs);
    }
  }

  removeMarksAndNosRef(ref: string) {
    const idx = this.marksAndNosRefs.indexOf(ref);
    if (idx > -1) {
      this.marksAndNosRefs.splice(idx, 1);
      this.saveRefs('marksAndNosRefs', this.marksAndNosRefs);
      if (this.invoiceForm.get('marksAndNos')?.value === ref) {
        this.invoiceForm.get('marksAndNos')?.setValue('');
      }
    }
  }

  onNoOfKindOfPackageBlur() {
    const value = this.invoiceForm.get('noOfKindOfPackage')?.value;
    if (value && !this.noOfKindOfPackageRefs.includes(value)) {
      this.noOfKindOfPackageRefs.push(value);
      this.saveRefs('noOfKindOfPackageRefs', this.noOfKindOfPackageRefs);
    }
  }

  removeNoOfKindOfPackageRef(ref: string) {
    const idx = this.noOfKindOfPackageRefs.indexOf(ref);
    if (idx > -1) {
      this.noOfKindOfPackageRefs.splice(idx, 1);
      this.saveRefs('noOfKindOfPackageRefs', this.noOfKindOfPackageRefs);
      if (this.invoiceForm.get('noOfKindOfPackage')?.value === ref) {
        this.invoiceForm.get('noOfKindOfPackage')?.setValue('');
      }
    }
  }

  // Add property to hold areas
  public areas: any[] = [];

  // Removed loadConsignees - now handled by loadBuyers for better performance

  // Load buyer orders from API or service
  loadBuyerOrders(): void {
    console.log('🔄 Loading buyer orders...');
    this.invoiceService.getAllBuyerOrders().subscribe({
      next: (orders: any[]) => {
        console.log('✅ Buyer orders loaded:', orders);
        this.buyerOrders = orders;
        this.filteredBuyerOrders = [...orders]; // Initialize filtered list
        console.log('📋 Available orders for dropdown:', this.buyerOrders.map(o => ({ id: o.id, orderNo: o.orderNo, buyerName: o.buyerName })));
      },
      error: (error: any) => {
        console.error('❌ Error loading buyer orders:', error);
        this.snackBar.open('Failed to load buyer orders', 'Close', {
          duration: 3000
        });
      }
    });
  }

  // Load buyers from MasterService (also populates consignees)
  loadBuyers(): void {
    console.log('🔄 Loading buyers/consignees...');
    this.masterService.getAllBuyerList().subscribe({
      next: (buyers: any[]) => {
        console.log('✅ Buyers loaded successfully:', buyers.length);
        this.buyers = buyers;
        this.consignees = buyers; // Same data for consignees
        this.updateBuyersListText();
      },
      error: (error: any) => {
        console.error('❌ Error loading buyers:', error);
        this.snackBar.open('Failed to load buyers', 'Close', { duration: 3000 });
      }
    });
  }

  updateBuyersListText(): void {
    this.buyersListText = this.buyers.map(buyer =>
      `${buyer.customerName}\n${buyer.customerAddress}\n${buyer.country} ${buyer.zipCode}`
    ).join('\n\n');
  }

  // When buyer order selection changes
  onBuyerOrderChange(orderId: string): void {
    this.selectedBuyerOrderId = orderId;
    const selectedOrder = this.buyerOrders.find(order => order.id === orderId);
    if (selectedOrder) {
      this.invoiceForm.patchValue({
        buyersOrderNo: selectedOrder.orderNo, // Use actual order number, not ObjectId
        buyersOrderDate: selectedOrder.orderDate
      });
    }
  }

  // New methods for autocomplete functionality
  onBuyerOrderInputChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    console.log('🔍 Buyer order input changed:', value);

    // Simple implementation - just add to refs if not exists
    if (value && value.trim()) {
      const upperValue = value.toUpperCase();
      const existsInOrders = this.buyerOrders.some(order =>
        order.orderNo.toLowerCase() === value.toLowerCase()
      );
      const existsInRefs = this.buyersOrderNoRefs.includes(upperValue);

      if (!existsInOrders && !existsInRefs) {
        this.buyersOrderNoRefs.unshift(upperValue);
        console.log('➕ Added new buyer order ref:', upperValue);
      }
    }
  }

  onBuyerOrderSelected(orderNo: string): void {
    console.log('🔍 Buyer order selected:', orderNo);
    console.log('🔍 Available buyer orders:', this.buyerOrders);

    // Find the selected order
    const selectedOrder = this.buyerOrders.find(order => order.orderNo === orderNo);
    if (selectedOrder) {
      console.log('✅ Found matching order:', selectedOrder);
      this.selectedBuyerOrderId = selectedOrder.id;
      this.invoiceForm.patchValue({
        buyersOrderNo: orderNo,
        buyersOrderDate: selectedOrder.orderDate
      });
      console.log('✅ Form updated with order details');
    } else {
      console.log('⚠️ Order not found in list, using custom entry');
      // Custom entry
      this.invoiceForm.patchValue({
        buyersOrderNo: orderNo
      });
    }
  }

  onBuyersOrderNoBlur(): void {
    const value = this.invoiceForm.get('buyersOrderNo')?.value;
    if (value && !this.buyersOrderNoRefs.includes(value)) {
      this.buyersOrderNoRefs.push(value);
      this.saveRefs('buyersOrderNoRefs', this.buyersOrderNoRefs);
    }
  }

  removeBuyersOrderNoRef(ref: string): void {
    const index = this.buyersOrderNoRefs.indexOf(ref);
    if (index > -1) {
      this.buyersOrderNoRefs.splice(index, 1);
      this.saveRefs('buyersOrderNoRefs', this.buyersOrderNoRefs);
    }
  }

  // When consignee selection changes (legacy method - now handled by onBuyerSelected)
  onConsigneeChange(buyerId: string): void {
    this.selectedConsigneeId = buyerId;
    this.selectedBuyer = this.buyers.find(b => b._id === buyerId);
    if (this.selectedBuyer) {
      // Build full uppercase consignee text
      const consigneeText = `${this.selectedBuyer.customerName?.toUpperCase() || ''},\n${this.selectedBuyer.customerAddress?.toUpperCase() || ''},\n${this.selectedBuyer.country?.toUpperCase() || ''} ${this.selectedBuyer.zipCode || ''}`;
      this.invoiceForm.patchValue({
        consignee: consigneeText
      });
    }
  }

  // Load packing lists from the API
  loadPackingLists(): void {
    this.isLoading = true;
    this.invoiceService.getAllPackingLists().subscribe({
      next: (packingLists: any[]) => {
        this.packingLists = packingLists;
        console.log('Loaded packing lists:', packingLists);

        if (packingLists.length > 0) {
          // Show dialog or prompt to select a packing list
          this.selectPackingList();
        } else {
          this.snackBar.open('No packing lists found', 'Close', {
            duration: 3000
          });
          this.isLoading = false;
        }
      },
      error: (error: any) => {
        console.error('Error loading packing lists:', error);
        this.snackBar.open('Failed to load packing lists', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  // Simple method to select a packing list
  selectPackingList(): void {
    // For simplicity, just show a snackbar with options
    const packingListOptions = this.packingLists.map(pl => pl._id).join(', ');
    this.snackBar.open(`Select a packing list ID: ${packingListOptions}`, 'Close', {
      duration: 10000
    });

    // In a real implementation, you would show a dialog or dropdown
    // For now, just use the first packing list
    if (this.packingLists.length > 0) {
      this.loadGoodsFromPackingList(this.packingLists[0]._id);
    } else {
      this.isLoading = false;
    }
  }

  // Load goods from packing list
  loadGoodsFromPackingList(packingListId: string): void {
    if (!packingListId) {
      this.isLoading = false;
      return;
    }

    this.invoiceService.getPackingListById(packingListId).subscribe({
      next: (packingList: any) => {
        if (packingList && packingList.items && packingList.items.length > 0) {
          // Clear existing goods
          while (this.goods.length) {
            this.goods.removeAt(0);
          }

          // Add goods from packing list
          packingList.items.forEach((item: any) => {
            this.goods.push(this.createGoodsItem(
              item.quality || '',
              item.design || '',
              item.pieces || 0,
              item.area || 0,
              0 // Default rate
            ));
          });

          // Update form with packing list data if available
          if (packingList.buyerOrder) {
            this.invoiceForm.patchValue({
              buyersOrderNo: packingList.buyerOrder.orderNo || '', // Use actual order number
              buyersOrderDate: this.parseDate(packingList.buyerOrder.orderDate || '')
            });
          }

          if (packingList.buyer) {
            this.invoiceForm.patchValue({
              consignee: packingList.buyer._id || '' // Use ObjectId
            });
          }

          // Calculate totals
          this.calculateTotals();

          this.snackBar.open('Goods loaded from packing list', 'Close', {
            duration: 3000
          });
        } else {
          this.snackBar.open('No items found in packing list', 'Close', {
            duration: 3000
          });
        }


        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading packing list:', error);
        this.snackBar.open('Failed to load packing list', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  get goods(): FormArray {
    return this.invoiceForm.get('goods') as FormArray;
  }

  addGoodsItem(): void {
    this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
  }

  removeGoodsItem(index: number): void {
    if (this.goods.length > 1) {
      this.goods.removeAt(index);
      this.calculateTotals();
    }
  }

  calculateTotals(): void {
    let totalPieces = 0;
    let totalQuantity = 0;
    let totalFOB = 0;

    this.goods.controls.forEach(group => {
      const pieces = parseInt(group.get('pieces')?.value) || 0;
      const quantitySqMeter = parseFloat(group.get('quantitySqMeter')?.value) || 0;
      const rateFOB = parseFloat(group.get('rateFOB')?.value) || 0;

      // Calculate amount FOB
      const amountFOB = quantitySqMeter * rateFOB;
      group.get('amountFOB')?.setValue(amountFOB.toFixed(2), { emitEvent: false });

      totalPieces += pieces;
      totalQuantity += quantitySqMeter;
      totalFOB += amountFOB;
    });

    this.totalPieces = totalPieces;
    this.totalQuantity = parseFloat(totalQuantity.toFixed(2));
    this.totalFOB = totalFOB.toFixed(2);
    this.totalCifEuro = totalFOB.toFixed(2); // For backward compatibility

    // Calculate additional charges
    const addedFreight = parseFloat(this.invoiceForm.get('addedFreight')?.value || '0');
    const insurance = parseFloat(this.invoiceForm.get('insurance')?.value || '0');

    // Calculate IGST
    const igstPercentage = parseFloat(this.invoiceForm.get('igstPercentage')?.value || '0');
    const igstAmount = (totalFOB * igstPercentage) / 100;
    this.invoiceForm.get('igst')?.setValue(igstAmount.toFixed(2), { emitEvent: false });

    // Calculate grand total
    const grandTotal = totalFOB + addedFreight + insurance + igstAmount;
    this.grandTotal = grandTotal.toFixed(2);

    // Update amount in words
    const amountInWords = this.convertNumberToWords(Number(grandTotal));
    this.invoiceForm.get('amountChargeableWords')?.setValue(`EURO ${amountInWords} ONLY`, { emitEvent: false });

    // Auto-populate "No & Kind of Package" with total pieces only when coming from packing list
    const isFromPackingList = history.state && history.state.fromPackingList;
    if (this.totalPieces > 0 && isFromPackingList) {
      const packageDescription = `${this.totalPieces} BALES`;
      this.invoiceForm.get('noOfKindOfPackage')?.setValue(packageDescription, { emitEvent: false });
      console.log('📦 Auto-updated No & Kind of Package from packing list:', packageDescription);
    }
  }

  submitForm(): void {
    // Patch consignee to string before submit
    const consigneeValue = this.invoiceForm.get('consignee')?.value;
    if (consigneeValue && typeof consigneeValue === 'object') {
      // If it's an object, build the string
      const c: any = consigneeValue;
      const consigneeString = [
        c.customerName || c.name || '',
        c.customerAddress || c.address || '',
        c.country || '',
        c.zipCode || ''
      ].filter(Boolean).join(', ');
      this.invoiceForm.get('consignee')?.setValue(consigneeString, { emitEvent: false });
    }
    if (this.isUpdateMode) {
      this.updateInvoice();
    } else {
      this.createInvoice();
    }
  }

  createInvoice(): void {
    if (this.invoiceForm.valid) {
      this.isSubmitting = true;

      // Prepare the invoice data
      const formData = this.invoiceForm.getRawValue();
      // Send Date objects directly to service (service will handle ISO conversion)
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate, // Add the Date object
        area: formData.area, // Add area ObjectId
        buyersOrderNo: formData.buyersOrderNo, // Should be ObjectId string
        buyersOrderDate: buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || '' // Use base64 for database storage
      } as any;

      // Use the service to create the invoice (handles backend structure)
      this.invoiceService.createInvoice(invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice saved successfully!', 'Close', {
            duration: 3000
          });
          // this.createNewInvoice(); // Removed to prevent generating a new invoice after save
          this.loadInvoices();
        },
        error: (error) => {
          console.error('Error saving invoice:', error);
          this.isSubmitting = false;
          this.snackBar.open('Failed to save invoice. Please try again.', 'Close', {
            duration: 3000
          });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', {
        duration: 3000
      });
    }
  }

  updateInvoice(): void {
    if (this.invoiceForm.valid && this.loadedInvoiceId) {
      this.isSubmitting = true;
      const formData = this.invoiceForm.getRawValue();

      // Do NOT append date to invoice number, keep as RE-XXX only
      // Send Date objects directly to service (service will handle ISO conversion)
      const invoiceDate = formData.invoiceDate ? new Date(formData.invoiceDate) : new Date();
      const buyersOrderDate = formData.buyersOrderDate ? new Date(formData.buyersOrderDate) : null;
      delete formData.invoiceDate;

      // Compose InvoiceData
      const invoiceData: InvoiceData = {
        ...formData,
        invoiceDate: invoiceDate, // Add the Date object
        area: formData.area, // Add area ObjectId
        buyersOrderNo: formData.buyersOrderNo, // Should be ObjectId string
        buyersOrderDate: buyersOrderDate,
        totalPieces: this.totalPieces,
        totalQuantity: this.totalQuantity,
        totalCifEuro: this.totalCifEuro.toString(),
        grandTotal: this.grandTotal.toString(),
        signature: this.signatureBase64 || '' // Use base64 for database storage
      } as any;

      console.log('🔄 Updating invoice with ID:', this.loadedInvoiceId);
      console.log('🔄 Invoice data for update:', invoiceData);

      this.invoiceService.updateInvoice(this.loadedInvoiceId, invoiceData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.snackBar.open('Invoice updated successfully!', 'Close', { duration: 3000 });
          // this.createNewInvoice(); // Removed to keep user on the same invoice after update
          this.loadInvoices();
          this.isUpdateMode = false;
          this.loadedInvoiceId = null;
        },
        error: (error) => {
          this.isSubmitting = false;
          this.snackBar.open('Failed to update invoice. Please try again.', 'Close', { duration: 3000 });
        }
      });
    } else {
      this.invoiceForm.markAllAsTouched();
      this.snackBar.open('Please fill all required fields.', 'Close', { duration: 3000 });
    }
  }

  // Invoice selection methods
  createNewInvoice(): void {
    this.isLoading = true;

    // Get a new invoice number from the API
    this.invoiceService.generateInvoiceNumber().subscribe({
      next: (response) => {
        console.log('✅ Received invoice number from backend:', response);
        let invoiceNo = 'RE-755';
        if (response && response.invoiceNumber) {
          // Use the full invoice number from backend (includes date)
          invoiceNo = response.invoiceNumber;
        }
        console.log('🔢 Using invoice number:', invoiceNo);
        // Reset the form with only the exporter data pre-filled
        this.invoiceForm.reset({
          invoiceNumber: invoiceNo, // Always just RE-XXX
          invoiceDate: new Date(), // Set to current date
          exporterRef: '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          // All other fields are left empty for user input
          buyersOrderNo: '',
          buyersOrderDate: null,
          otherReferences: '',
          consignee: '',
          buyerIfOther: '',
          preCarriageBy: '',
          placeOfReceipt: '',
          originCountry: 'INDIA', // Default country of origin
          destinationCountry: '',
          vesselNo: '',
          portOfLoading: '',
          portOfDischarge: '',
          finalDestination: '',
          marksAndNos: '',
          noOfKindOfPackage: '',
          descriptionOfGoods: 'Indian Hand-Knotted Woolen Carpets',
          area: '', // Initialize area field
          addedFreight: '0.00',
          amountChargeableWords: '',
          insurance: '0.00',
          igstPercentage: '0.00',
          igst: '0.00'
        });

        // Clear the goods array
        while (this.goods.length) {
          this.goods.removeAt(0);
        }
        // Always add one empty row for new invoice
        this.goods.push(this.createGoodsItem('', '', 0, 0, 0));

        // Clear signature
        this.signatureImageUrl = null;
        this.signatureBase64 = null;

        // Load packing list data automatically
        this.loadPackingLists();

        // Show the invoice form
        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.selectedOldInvoice = '';

        // Set to create mode for new invoice
        this.isUpdateMode = false;
        this.loadedInvoiceId = null;

        // Notify user about the new invoice number
        this.snackBar.open(`New invoice created with number: ${invoiceNo}`, 'Close', {
          duration: 3000
        });
      },
      error: (error) => {
        // Fallback: always start at RE-755 if API fails
        this.invoiceForm.reset({
          invoiceNumber: 'RE-755',
          invoiceDate: new Date(),
          exporterRef: '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          buyersOrderNo: '',
          buyersOrderDate: null,
          otherReferences: '',
          consignee: '',
          buyerIfOther: '',
          preCarriageBy: '',
          placeOfReceipt: '',
          originCountry: 'INDIA',
          destinationCountry: '',
          vesselNo: '',
          portOfLoading: '',
          portOfDischarge: '',
          finalDestination: '',
          marksAndNos: '',
          noOfKindOfPackage: '',
          descriptionOfGoods: '',
          area: '', // Initialize area field
          addedFreight: '0.00',
          amountChargeableWords: '',
        });
        while (this.goods.length) {
          this.goods.removeAt(0);
        }
        this.goods.push(this.createGoodsItem('', '', 0, 0, 0));

        // Clear signature
        this.signatureImageUrl = null;
        this.signatureBase64 = null;

        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.selectedOldInvoice = '';
        this.isLoading = false;

        // Set to create mode for new invoice
        this.isUpdateMode = false;
        this.loadedInvoiceId = null;

        this.calculateTotals();
        this.snackBar.open('New invoice created with number: RE-755 (fallback)', 'Close', {
          duration: 3000
        });
      }
    });
  }

  loadInvoices(): void {
    console.log('📡 Loading old invoices from exportinvoices collection...');
    this.isLoading = true;
    this.invoiceService.getAllInvoices().subscribe({
      next: (invoices) => {
        console.log('✅ Raw invoices received:', invoices);
        console.log('✅ Number of invoices:', invoices.length);

        this.oldInvoices = invoices.map(invoice => ({
          id: invoice.id || '',
          number: invoice.invoiceNumber,
          date: this.extractDateFromInvoiceNumber(invoice.invoiceNumber)
        }));

        console.log('✅ Processed old invoices:', this.oldInvoices);
        this.isLoading = false;

        if (invoices.length === 0) {
          console.log('⚠️ No invoices found in database');
          this.snackBar.open('No invoices found in database', 'Close', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('❌ Error loading invoices:', error);
        this.snackBar.open('Failed to load invoices. Please try again.', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  extractDateFromInvoiceNumber(invoiceNumber: string): string {
    // Extract date from invoice number format like "RE-659 11/03/2014"
    const parts = invoiceNumber.split(' ');
    return parts.length > 1 ? parts[1] : '';
  }

  toggleOldInvoices(): void {
    this.showOldInvoices = !this.showOldInvoices;
    if (this.showOldInvoices && this.oldInvoices.length === 0) {
      this.loadInvoices();
    }
  }

  loadOldInvoice(): void {
    if (!this.selectedOldInvoice) {
      return;
    }

    this.isLoading = true;
    console.log('📡 Loading old invoice:', this.selectedOldInvoice);

    this.invoiceService.getInvoiceById(this.selectedOldInvoice).subscribe({
      next: (invoice) => {
        console.log('✅ Received invoice data:', invoice);

        // Log specific field values from database
        console.log('🔍 Database field values:');
        console.log('  exporterRef:', invoice.exporterRef);
        console.log('  buyersOrderNo:', invoice.buyersOrderNo);
        console.log('  buyersOrderDate:', invoice.buyersOrderDate);
        console.log('  otherReferences:', invoice.otherReferences);
        console.log('  preCarriageBy:', invoice.preCarriageBy);
        console.log('  placeOfReceipt:', invoice.placeOfReceipt);
        console.log('  vesselNo:', invoice.vesselNo);
        console.log('  portOfLoading:', invoice.portOfLoading);
        console.log('  portOfDischarge:', invoice.portOfDischarge);
        console.log('  finalDestination:', invoice.finalDestination);
        console.log('  marksAndNos:', invoice.marksAndNos);
        console.log('  noOfKindOfPackage:', invoice.noOfKindOfPackage);
        console.log('  descriptionOfGoods:', invoice.descriptionOfGoods);
        console.log('  goods:', invoice.goods);

        // Clear the goods array
        while (this.goods.length) {
          this.goods.removeAt(0);
        }

        // Handle signature if present
        if (invoice.signature) {
          this.signatureBase64 = invoice.signature;
          this.signatureImageUrl = invoice.signature; // Use base64 directly for preview
          console.log('✅ Signature loaded from database');
        } else {
          this.signatureBase64 = null;
          this.signatureImageUrl = null;
        }

        // Parse dates properly
        const invoiceDate = invoice.invoiceDate ? new Date(invoice.invoiceDate) : new Date();
        const buyersOrderDate = invoice.buyersOrderDate ? this.parseDate(invoice.buyersOrderDate) : null;

        console.log('📅 Date parsing:');
        console.log('  Raw invoice date:', invoice.invoiceDate);
        console.log('  Parsed invoice date:', invoiceDate);
        console.log('  Raw buyer order date:', invoice.buyersOrderDate);
        console.log('  Parsed buyer order date:', buyersOrderDate);

        // Find and set the consignee/buyer after ensuring buyers are loaded
        if (invoice.consignee) {
          console.log('🔍 Looking for consignee match:', invoice.consignee);
          console.log('🔍 Available buyers:', this.buyers.length);

          // If buyers not loaded, load them first
          if (this.buyers.length === 0) {
            console.log('⏳ Loading buyers first...');
            this.loadBuyersPromise().then(() => {
              this.matchAndSetConsignee(invoice.consignee);
            });
          } else {
            this.matchAndSetConsignee(invoice.consignee);
          }
        }

        // Load the invoice data into the form with proper structure
        console.log('📝 Patching form with invoice data...');
        console.log('📝 Invoice number:', invoice.invoiceNumber);
        console.log('📝 Invoice date:', invoiceDate);
        console.log('📝 Buyer order no:', invoice.buyersOrderNo);
        console.log('📝 Buyer order date:', buyersOrderDate);
        console.log('📝 Consignee:', invoice.consignee);

        const patchData = {
          invoiceNumber: invoice.invoiceNumber || 'RE-755',
          invoiceDate: invoiceDate,
          exporterRef: invoice.exporterRef || '',
          exporter: 'M/S. RACHIN EXPORTS,\nMADHOSINGH, P.O. AURAI,\nDIST. BHADOHI-231001, INDIA',
          buyersOrderNo: invoice.buyersOrderNo || '',
          buyersOrderDate: buyersOrderDate,
          otherReferences: invoice.otherReferences || '',
          consignee: invoice.consignee || '',
          buyerIfOther: invoice.buyerIfOther || 'SAME',
          preCarriageBy: invoice.preCarriageBy || '',
          placeOfReceipt: invoice.placeOfReceipt || '',
          originCountry: invoice.originCountry || 'INDIA',
          destinationCountry: invoice.destinationCountry || '',
          vesselNo: invoice.vesselNo || '',
          portOfLoading: invoice.portOfLoading || '',
          portOfDischarge: invoice.portOfDischarge || '',
          finalDestination: invoice.finalDestination || '',
          marksAndNos: invoice.marksAndNos || '',
          noOfKindOfPackage: invoice.noOfKindOfPackage || '',
          descriptionOfGoods: invoice.descriptionOfGoods || 'Indian Hand-Knotted Woolen Carpets',
          area: invoice.area || '',
          addedFreight: invoice.addedFreight || '0.00',
          amountChargeableWords: invoice.amountChargeableWords || '',
          insurance: '0.00',
          igstPercentage: '0.00',
          igst: '0.00'
        };

        console.log('📝 Patch data object:', patchData);
        console.log('📝 Form before patch:', this.invoiceForm.value);

        this.invoiceForm.patchValue(patchData);

        console.log('📝 Form after patch:', this.invoiceForm.value);

        // Check specific field values
        console.log('📝 Invoice Number field:', this.invoiceForm.get('invoiceNumber')?.value);
        console.log('📝 Consignee field:', this.invoiceForm.get('consignee')?.value);
        console.log('📝 Exporter Ref field:', this.invoiceForm.get('exporterRef')?.value);
        console.log('📝 Buyer Order No field:', this.invoiceForm.get('buyersOrderNo')?.value);
        console.log('📝 Buyer Order Date field:', this.invoiceForm.get('buyersOrderDate')?.value);
        console.log('📝 Other References field:', this.invoiceForm.get('otherReferences')?.value);
        console.log('✅ Form patched successfully');

        // Handle goods loading based on source
        console.log('📦 Loading goods data:', invoice.goods);
        console.log('📦 History state:', history.state);

        // Clear existing goods first
        while (this.goods.length) {
          this.goods.removeAt(0);
        }

        // Check if we're coming from packing list
        const isFromPackingList = history.state && history.state.fromPackingList;

        if (isFromPackingList) {
          console.log('📦 Coming from packing list - goods will be loaded from packing list data');
          // Add a temporary empty item that will be replaced by packing list goods
          this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
        } else {
          // Normal invoice loading - load goods from invoice
          if (invoice.goods && invoice.goods.length > 0) {
            console.log('📦 Found', invoice.goods.length, 'goods items from invoice');
            invoice.goods.forEach((item, index) => {
              console.log(`📦 Adding goods item ${index + 1}:`, item);
              this.goods.push(this.createGoodsItem(
                item.quality || '',
                item.design || '',
                item.pieces || 0,
                item.quantity || 0,
                item.rate || 0
              ));
            });
            console.log('📦 Invoice goods loaded successfully');
          } else {
            console.log('📦 No goods data found in invoice, adding default empty item');
            // Add at least one empty item
            this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
          }
        }

        // Show the invoice form
        console.log('🎯 Setting form visibility and calculating totals...');
        this.showInvoiceForm = true;
        this.showOldInvoices = false;
        this.isLoading = false;

        // Calculate totals
        this.calculateTotals();

        // Always enable update mode when loading existing invoice
        console.log('🔄 Loading existing invoice - enabling update mode');
        this.isUpdateMode = true;
        this.loadedInvoiceId = invoice.id || null;

        // If coming from packing list, load packing list goods immediately
        if (isFromPackingList) {
          console.log('🔄 Coming from packing list - loading packing list goods immediately');
          console.log('🔄 Invoice ID for packing list:', invoice.id);

          this.invoiceService.getPackingListsByInvoiceId(invoice.id || this.selectedOldInvoice).subscribe({
            next: (packingLists: any[]) => {
              console.log('📦 Received packing lists from service:', packingLists);
              console.log('📦 Number of packing lists:', packingLists ? packingLists.length : 0);

              if (packingLists && packingLists.length > 0) {
                console.log('📦 Loading goods from', packingLists.length, 'packing lists');
                this.loadGoodsFromPackingLists(packingLists);
              } else {
                console.log('📦 No packing lists found for this invoice');
              }
            },
            error: (error) => {
              console.error('❌ Error loading packing lists for invoice:', error);
            }
          });
        }

        // Force change detection to ensure form is visible
        setTimeout(() => {
          console.log('🔄 Forcing change detection for form visibility');
          this.showInvoiceForm = true;
          this.showOldInvoices = false;
        }, 100);

        console.log('🎯 Form state - showInvoiceForm:', this.showInvoiceForm);
        console.log('🎯 Form state - showOldInvoices:', this.showOldInvoices);
        console.log('🎯 Form state - isLoading:', this.isLoading);
        console.log('🎯 Update mode enabled:', this.isUpdateMode);

        // Force change detection and check DOM
        setTimeout(() => {
          console.log('🔄 Triggering change detection...');
          console.log('🔄 Final form values:', this.invoiceForm.value);

          // Check if form element exists in DOM
          const formElement = document.querySelector('.invoice-container');
          console.log('🔄 Form element in DOM:', formElement ? 'EXISTS' : 'NOT FOUND');

          if (formElement) {
            console.log('🔄 Form element styles:', window.getComputedStyle(formElement).display);
            console.log('🔄 Form element visibility:', window.getComputedStyle(formElement).visibility);
          }

          // Double check component state
          console.log('🔄 Component showInvoiceForm:', this.showInvoiceForm);
          console.log('🔄 Component showOldInvoices:', this.showOldInvoices);

          // Check if selectedBuyer is set properly
          console.log('🔄 Selected Buyer:', this.selectedBuyer);
          console.log('🔄 Selected Buyer ID:', this.selectedConsigneeId);

          // Check form control states
          console.log('🔄 Form controls status:');
          Object.keys(this.invoiceForm.controls).forEach(key => {
            const control = this.invoiceForm.get(key);
            if (control) {
              console.log(`  ${key}: value="${control.value}", disabled=${control.disabled}, valid=${control.valid}`);
            }
          });
        }, 500);

        console.log('✅ Invoice loaded successfully in edit mode');
      },
      error: (error) => {
        console.error('❌ Error loading invoice:', error);
        this.snackBar.open('Failed to load invoice. Please try again.', 'Close', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  // Helper methods
  private generateRandomNumber(): string {
    // Generate a random 3-digit number
    return (Math.floor(Math.random() * 900) + 100).toString();
  }

  // Get all packing lists
  getAllPackingLists(): Observable<any[]> {
    return this.http.get<any[]>(`${this.packingListApiUrl}/exportPacking`).pipe(
      map(packingLists => {


        return Array.isArray(packingLists) ? packingLists : [];
      }),
      catchError(error => {
        console.error('Error fetching packing lists:', error);
        return of([]);
      })
    );
  }

  // Get packing list by ID
  getPackingListById(id: string): Observable<any> {
    return this.http.get<any>(`${this.packingListApiUrl}/exportPacking/${id}`).pipe(
      map(response => {
        if (response && response.data) {
          return response.data;
        } else if (response) {
          return response;
        } else {
          return null;
           }
      }),
      catchError(error => {
        console.error('Error fetching packing list:', error);
        // Return sample data based on ID
                  return of(null);
      })
    );
  }

  // Generate invoice number
  generateInvoiceNumber(): Observable<any> {
    // Always start at RE-755 and increment from there
    return this.http.get<any>('http://localhost:2000/api/invoices/latest').pipe(
      map((response: any) => {
        let nextNumber = 755; // Start at 755
        if (response && response.document && response.document.invoiceNumber) {
          // Extract the number part from the latest invoice number (format: RE-XXX DD/MM/YYYY)
          const invoiceNumberParts = response.document.invoiceNumber.split(' ');
          const invoiceNo = invoiceNumberParts[0]; // RE-XXX
          const match = invoiceNo.match(/RE-(\d+)/);
          if (match && match[1]) {
            const lastNum = parseInt(match[1], 10);
            nextNumber = lastNum >= 755 ? lastNum + 1 : 755;
          }
        }
        const invoiceNumber = `RE-${nextNumber}`;
        const today = new Date();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const year = today.getFullYear();
        const dateStr = `${day}/${month}/${year}`;
        return { invoiceNumber: `${invoiceNumber} ${dateStr}` };
      }),
      catchError((error: any) => {
        // Fallback: always start at RE-755 if no previous invoice
        const today = new Date();
        const day = String(today.getDate()).padStart(2, '0');
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const year = today.getFullYear();
        const dateStr = `${day}/${month}/${year}`;
        return of({ invoiceNumber: `RE-755 ${dateStr}` });
      })
    );
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}/${month}/${year}`;
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;

    // Parse date in format DD/MM/YYYY
    const parts = dateStr.split('/');
    if (parts.length !== 3) return null;

    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed in Date
    const year = parseInt(parts[2], 10);

    if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

    return new Date(year, month, day);
  }

  // Convert number to words
  private convertNumberToWords(num: number): string {
    const ones = ['', 'ONE', 'TWO', 'THREE', 'FOUR', 'FIVE', 'SIX', 'SEVEN', 'EIGHT', 'NINE', 'TEN', 'ELEVEN', 'TWELVE', 'THIRTEEN', 'FOURTEEN', 'FIFTEEN', 'SIXTEEN', 'SEVENTEEN', 'EIGHTEEN', 'NINETEEN'];
    const tens = ['', '', 'TWENTY', 'THIRTY', 'FORTY', 'FIFTY', 'SIXTY', 'SEVENTY', 'EIGHTY', 'NINETY'];

    const numStr = num.toString();
    const decimalParts = numStr.split('.');
    const wholePart = parseInt(decimalParts[0]);
    const decimalPart = decimalParts.length > 1 ? parseInt(decimalParts[1]) : 0;

    const convertLessThanOneThousand = (n: number): string => {
      if (n === 0) {
        return '';
      }

      if (n < 20) {
        return ones[n];
      }

      if (n < 100) {
        return tens[Math.floor(n / 10)] + (n % 10 !== 0 ? ' ' + ones[n % 10] : '');
      }

      return ones[Math.floor(n / 100)] + ' HUNDRED' + (n % 100 !== 0 ? ' AND ' + convertLessThanOneThousand(n % 100) : '');
    };

    const convertNumber = (n: number): string => {
      if (n === 0) {
        return 'ZERO';
      }

      let result = '';

      // Handle millions
      if (n >= 1000000) {
        result += convertLessThanOneThousand(Math.floor(n / 1000000)) + ' MILLION ';
        n %= 1000000;
      }

      // Handle thousands
      if (n >= 1000) {
        result += convertLessThanOneThousand(Math.floor(n / 1000)) + ' THOUSAND ';
        n %= 1000;
      }

      // Handle hundreds and tens
      if (n > 0) {
        result += convertLessThanOneThousand(n);
      }

      return result.trim();
    };

    let result = convertNumber(wholePart);

    // Handle decimal part
    if (decimalPart > 0) {
      // Format decimal part to always have 2 digits
      const decimalStr = decimalParts[1].padEnd(2, '0').substring(0, 2);
      result += ' POINT ' + convertNumber(parseInt(decimalStr));
    }

    return result;
  }

  // Format Exporter address so that 'MADHOSINGH, P.O. AURAI' is always on the same line
  formatExporterText(raw: string): string {
    return raw;
  }

  async onSaveShippingDetails() {
    // Confirm with SweetAlert
    const result = await this.sweetalertService.confirm({
      title: 'Save Shipping Details?',
      text: 'Are you sure you want to save the shipping details and go to Packing List?',
      icon: 'question',
      confirmButtonText: 'Yes, save',
      cancelButtonText: 'Cancel'
    });
    if (result.isConfirmed) {
      this.isSubmitting = true;

      // Direct navigation to packing list
      console.log('🔄 Redirecting directly to packing list...');
      this.router.navigate(['/admin/export-packing-list']).then((success) => {
        console.log('✅ Navigation successful:', success);
        this.isSubmitting = false;
        if (!success) {
          console.log('⚠️ Navigation failed, trying alternative route...');
          window.location.href = '/admin/export-packing-list';
        }
      }).catch((error) => {
        console.error('❌ Navigation error:', error);
        this.isSubmitting = false;
        window.location.href = '/admin/export-packing-list';
      });
      return;

      // Save only shipping details
      const shippingDetails = {
        vesselNo: this.invoiceForm.get('vesselNo')?.value,
        portOfDischarge: this.invoiceForm.get('portOfDischarge')?.value,
        placeOfReceipt: this.invoiceForm.get('placeOfReceipt')?.value,
        preCarriageBy: this.invoiceForm.get('preCarriageBy')?.value,
        // Add other relevant fields as needed
      };
      // Call your service to save shipping details (replace with actual API call)
      console.log('🚀 Saving shipping details for invoice:', this.loadedInvoiceId);
      console.log('🚀 Shipping details:', shippingDetails);

      this.invoiceService.saveShippingDetails(this.loadedInvoiceId!, shippingDetails).subscribe({
        next: (response) => {
          console.log('✅ Shipping details saved successfully:', response);
          this.isSubmitting = false;
          this.sweetalertService.success('Saved!', 'Shipping details saved successfully.');

          // Redirect to packing list for this invoice
          console.log('🔄 Redirecting to packing list with invoice ID:', this.loadedInvoiceId);

          // Try absolute path first
          this.router.navigate(['/admin/export-packing-list'], {
            queryParams: { invoiceId: this.loadedInvoiceId }
          }).then((success) => {
            console.log('✅ Navigation successful:', success);
            if (!success) {
              console.log('⚠️ Navigation failed, trying alternative route...');
              // Fallback: try without manufacturing in path
              window.location.href = `/admin/export-packing-list?invoiceId=${this.loadedInvoiceId}`;
            }
          }).catch((error) => {
            console.error('❌ Navigation error:', error);
            // Fallback: use window.location
            window.location.href = `/admin/export-packing-list?invoiceId=${this.loadedInvoiceId}`;
          });
        },
        error: (error) => {
          console.error('❌ Error saving shipping details:', error);
          this.isSubmitting = false;
          this.sweetalertService.error('Error', 'Failed to save shipping details.');
        }
      });
    }
  }

  // Method to handle navigation from packing list
  ngAfterViewInit() {
    // Check if we need to scroll to goods section
    setTimeout(() => {
      if (history.state && history.state.scrollToGoods) {
        this.scrollToGoodsTable();

        // If we have an invoice ID, load it
        if (history.state.invoiceId) {
          this.selectedOldInvoice = history.state.invoiceId;

          // If coming from packing list, load invoice first, then packing list goods
          if (history.state.fromPackingList) {
            console.log('🔄 Coming from packing list - loading invoice and then packing list goods');
            console.log('🔄 Initial form state - showInvoiceForm:', this.showInvoiceForm);
            console.log('🔄 Initial form state - showOldInvoices:', this.showOldInvoices);

            // Ensure we're not showing the old invoices list
            this.showOldInvoices = false;
            this.showInvoiceForm = false; // Will be set to true in loadOldInvoice

            this.loadOldInvoice();

            // Wait a bit for invoice to load, then load packing list goods
            setTimeout(() => {
              console.log('🔄 Loading packing lists for invoice ID:', history.state.invoiceId);
              console.log('🔄 Calling invoiceService.getPackingListsByInvoiceId...');

              this.invoiceService.getPackingListsByInvoiceId(history.state.invoiceId).subscribe({
                next: (packingLists: any[]) => {
                  console.log('📦 Received packing lists from service:', packingLists);
                  console.log('📦 Number of packing lists:', packingLists ? packingLists.length : 0);

                  if (packingLists && packingLists.length > 0) {
                    console.log('📦 Loading goods from', packingLists.length, 'packing lists');
                    // Load goods from all packing lists for this invoice
                    this.loadGoodsFromPackingLists(packingLists);
                  } else {
                    console.log('📦 No packing lists found for this invoice - adding empty goods');
                    // Clear existing goods and add empty one
                    while (this.goods.length) {
                      this.goods.removeAt(0);
                    }
                    this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
                  }
                },
                error: (error) => {
                  console.error('❌ Error loading packing lists for invoice:', error);
                  console.error('❌ Error details:', error.message);
                  // Add empty goods on error
                  while (this.goods.length) {
                    this.goods.removeAt(0);
                  }
                  this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
                }
              });
            }, 1000);
          } else {
            // Normal invoice loading
            this.loadOldInvoice();
          }
        }
      }
    }, 500);
  }

  // Method to scroll to goods table
  scrollToGoodsTable() {
    const goodsSection = document.querySelector('.goods-section');
    if (goodsSection) {
      goodsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  // Load goods from multiple packing lists for specific invoice
  loadGoodsFromPackingLists(packingLists: any[]) {
    console.log('📦 loadGoodsFromPackingLists called with:', packingLists);

    // Validate that we have packing lists
    if (!packingLists || packingLists.length === 0) {
      console.log('📦 No packing lists provided, adding empty goods item');
      // Clear existing goods
      while (this.goods.length) {
        this.goods.removeAt(0);
      }
      this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
      return;
    }

    // Clear existing goods
    while (this.goods.length) {
      this.goods.removeAt(0);
    }
    console.log('📦 Cleared existing goods');

    // Process all items from all packing lists for this specific invoice
    let allItems: any[] = [];
    packingLists.forEach((packingList, index) => {
      console.log(`📦 Processing packing list ${index + 1}:`, packingList);
      console.log(`📦 Packing list invoice: ${packingList.invoiceNo}`);

      if (packingList.items && Array.isArray(packingList.items)) {
        console.log('📦 Found', packingList.items.length, 'items in packing list');
        packingList.items.forEach((item: any, itemIndex: number) => {
          console.log(`📦 Item ${itemIndex + 1}:`, item);
        });
        allItems = [...allItems, ...packingList.items];
      } else {
        console.log('📦 No items found in this packing list');
      }
    });
    console.log('📦 Total items collected from all packing lists:', allItems.length);

    if (allItems.length === 0) {
      console.log('📦 No items found in any packing list, adding empty goods item');
      this.goods.push(this.createGoodsItem('', '', 0, 0, 0));
      return;
    }

    // Group items by quality and design
    const groupedItems = this.groupItemsByQualityAndDesign(allItems);
    console.log('📦 Grouped items:', groupedItems);

    // Add grouped items to the form
    Object.values(groupedItems).forEach((group: any) => {
      console.log('📦 Adding goods group:', group);
      this.goods.push(this.createGoodsItem(
        group.quality,
        group.design,
        group.pieces,
        group.area,
        0 // Default rate
      ));
    });

    console.log('📦 Final goods count:', this.goods.length);

    // Calculate totals
    this.calculateTotals();
    console.log('📦 Goods loading from packing lists completed');
  }

  // Helper method to group items by quality and design
  groupItemsByQualityAndDesign(items: any[]) {
    const grouped: {[key: string]: any} = {};
    
    items.forEach(item => {
      const key = `${item.quality}-${item.design}`;
      if (!grouped[key]) {
        grouped[key] = {
          quality: item.quality,
          design: item.design,
          pieces: 0,
          area: 0
        };
      }
      
      grouped[key].pieces += 1;
      grouped[key].area += parseFloat(String(item.area)) || 0;
    });
    
    return grouped;
  }
}
