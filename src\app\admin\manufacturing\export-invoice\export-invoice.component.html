<div class="invoice-selection-container">
  <div class="invoice-selection-header">
    <h2>Export Invoice</h2>
    <div class="invoice-selection-buttons">
      <button type="button" class="invoice-button new-button" (click)="createNewInvoice()" [disabled]="isLoading">
        <span *ngIf="isLoading && !showOldInvoices">Loading...</span>
        <span *ngIf="!isLoading || showOldInvoices">New Invoice</span>
      </button>
      <button type="button" class="invoice-button old-button" (click)="toggleOldInvoices()" [disabled]="isLoading && !showOldInvoices">
        <span *ngIf="isLoading && showOldInvoices && oldInvoices.length === 0">Loading...</span>
        <span *ngIf="!isLoading || !showOldInvoices || oldInvoices.length > 0">Old Invoices</span>
      </button>
    </div>
    <div class="old-invoices-dropdown" *ngIf="showOldInvoices">
      <form #invoiceSelectForm="ngForm">
        <mat-form-field appearance="outline" class="invoice-select-field">
          <mat-label>Select an Invoice</mat-label>
          <mat-select name="selectedInvoice" [(ngModel)]="selectedOldInvoice" [disabled]="isLoading">
            <mat-option value="">-- Select an Invoice --</mat-option>
            <mat-option *ngFor="let invoice of oldInvoices" [value]="invoice.id">
              {{ invoice.number }} - {{ invoice.date }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <button mat-raised-button color="primary" type="button" class="load-invoice-button"
                [disabled]="!selectedOldInvoice || isLoading" (click)="loadOldInvoice()">
          <span *ngIf="isLoading && selectedOldInvoice">Loading...</span>
          <span *ngIf="!isLoading || !selectedOldInvoice">Load Invoice</span>
        </button>
      </form>
      <div *ngIf="isLoading && oldInvoices.length === 0" class="loading-message">
        Loading invoices...
      </div>
      <div *ngIf="!isLoading && oldInvoices.length === 0" class="no-invoices-message">
        No invoices found.
      </div>
    </div>
  </div>
</div>

<mat-card class="invoice-container" *ngIf="showInvoiceForm">
  <form [formGroup]="invoiceForm" (ngSubmit)="submitForm()">
    <!-- Title Section -->

    <div class="invoice-title">INVOICE</div>
    <div class="title-section">
      <!-- Exporter and Consignee Section -->
      <div class="exporter-consignee-section">
        <div class="exporter-box">
          <div class="section-label"></div>
          <div class="section-content">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Exporter</mat-label>
              <textarea matInput rows="5" readonly formControlName="exporter" (input)="toUppercaseInput($event, 'exporter')">
M/S. RACHIN EXPORTS,
MADHOSINGH, P.O. AURAI,
DIST. BHADOHI-231001, INDIA
              </textarea>
            </mat-form-field>
          </div>
        </div>
        <div class="invoice-details-right">
          <div class="invoice-row">
            <div class="invoice-number-box">
              <div class="invoice-number-label"></div>
              <div class="invoice-date-container">
                <mat-form-field appearance="outline" class="invoice-number-field">
                  <mat-label>Invoice No.</mat-label>
                  <input matInput formControlName="invoiceNumber" readonly>
                </mat-form-field>

                <mat-form-field appearance="outline" class="invoice-date-field">
                  <mat-label>Date</mat-label>
                  <input matInput [matDatepicker]="invoiceDatePicker" formControlName="invoiceDate">
                  <mat-datepicker-toggle matSuffix [for]="invoiceDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #invoiceDatePicker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>

            <div class="exporter-ref-box">
              <div class="exporter-ref-label"></div>
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Exporter's Ref</mat-label>
                <input matInput formControlName="exporterRef"
                       [matAutocomplete]="autoExporterRef"
                       (blur)="onExporterRefBlur()"
                       (input)="toUppercaseInput($event, 'exporterRef')">
                        <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
                <mat-autocomplete #autoExporterRef="matAutocomplete">
                  <mat-option *ngFor="let ref of exporterRefs" [value]="ref">
                    <span>{{ ref | uppercase }}</span>
                    <button mat-icon-button type="button" (click)="removeExporterRef(ref); $event.stopPropagation();" aria-label="Remove Exporter's Ref" style="float:right; margin-left:8px;">
                      <mat-icon>close</mat-icon>
                    </button>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
            </div>
          </div>

          <div class="buyers-order-box">
            <div class="buyers-order-label"></div>
            <div class="buyers-order-container">
              <mat-form-field appearance="outline" class="buyers-order-field">
                <mat-label>Buyer's Order No.</mat-label>
                <input matInput formControlName="buyersOrderNo"
                       [matAutocomplete]="autoBuyersOrderNo"
                       (blur)="onBuyersOrderNoBlur()"
                       (input)="onBuyerOrderInputChange($event)"
                       (keyup)="toUppercaseInput($event, 'buyersOrderNo')">
                       <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
                <mat-autocomplete #autoBuyersOrderNo="matAutocomplete" (optionSelected)="onBuyerOrderSelected($event.option.value)">
                  <mat-option *ngFor="let order of buyerOrders" [value]="order.orderNo">
                    <span>{{ order.orderNo }} - {{ order.buyerName }}</span>
                  </mat-option>
                  <mat-option *ngFor="let ref of buyersOrderNoRefs" [value]="ref">
                    <span>{{ ref | uppercase }}</span>
                    <button mat-icon-button type="button" (click)="removeBuyersOrderNoRef(ref); $event.stopPropagation();" aria-label="Remove Order" style="float:right; margin-left:8px;">
                      <mat-icon>close</mat-icon>
                    </button>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>

              <mat-form-field appearance="outline" class="buyers-order-date-field">
                <mat-label>Date</mat-label>
                <input matInput [matDatepicker]="buyersOrderDatePicker" formControlName="buyersOrderDate">
                <mat-datepicker-toggle matSuffix [for]="buyersOrderDatePicker"></mat-datepicker-toggle>
                <mat-datepicker #buyersOrderDatePicker></mat-datepicker>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="consignee-section">
      <div class="consignee-box">
        <div class="section-label"></div>
        <div class="section-content">
          <mat-form-field appearance="outline" class="full-width consignee-field">
            <mat-label>Consignee</mat-label>
            <div class="consignee-textarea-dropdown">
              <textarea matInput formControlName="consignee" rows="3" readonly></textarea>
              <button mat-icon-button type="button" (click)="consigneeSelect.open()" style="margin-left:-40px; z-index:2;">
                <mat-icon>arrow_drop_down</mat-icon>
              </button>
              <mat-select #consigneeSelect class="consignee-select-overlay" (selectionChange)="toUppercaseSelect($event.value, 'consignee'); onConsigneeChange($event.value)" [value]="selectedBuyer?._id" disableOptionCentering panelClass="consignee-dropdown-two-rows">
                <mat-option *ngFor="let buyer of buyers" [value]="buyer._id">
                  <span style="white-space: pre-line;">
                    <b>{{buyer.customerName}}</b><br>
                    {{buyer.customerAddress}}<br>
                    {{buyer.country}} {{buyer.zipCode}}
                  </span>
                </mat-option>
              </mat-select>
            </div>
          </mat-form-field>
          <style>
            .consignee-field .consignee-textarea-dropdown {
              position: relative;
              display: flex;
              align-items: stretch;
            }
            .consignee-field textarea[matInput] {
              width: 100%;
              resize: none;
              z-index: 1;
            }
            .consignee-select-overlay {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              opacity: 0;
              z-index: 2;
              cursor: pointer;
            }
            .mat-select-panel.consignee-dropdown-two-rows {
              max-height: calc(48px * 2); /* Limit dropdown height to 2 options */
            }
          </style>
        </div>
      </div>
      <div class="buyer-details-right">
        <div class="other-ref-box">
          <div class="other-ref-label"></div>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Other Reference(s)</mat-label>
            <input matInput formControlName="otherReferences"
                   [matAutocomplete]="autoOtherReferencesRef"
                   (blur)="onOtherReferencesBlur()"
                   (input)="toUppercaseInput($event, 'otherReferences')">
                   <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
            <mat-autocomplete #autoOtherReferencesRef="matAutocomplete">
              <mat-option *ngFor="let ref of otherReferencesRefs" [value]="ref">
                <span>{{ ref | uppercase }}</span>
                <button mat-icon-button type="button" (click)="removeOtherReferencesRef(ref); $event.stopPropagation();" aria-label="Remove Other Reference" style="float:right; margin-left:8px;">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
        <div class="buyer-box">
          <div class="section-label"></div>
          <div class="section-content">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Buyer (if other than consignee)</mat-label>
              <mat-select formControlName="buyerIfOther" (selectionChange)="toUppercaseSelect($event.value, 'buyerIfOther')">
                <mat-option value="">-- Select Buyer --</mat-option>
                <mat-option *ngFor="let buyer of buyers" [value]="buyer._id">
                  <span style="white-space: pre-line;">
                    <b>{{buyer.customerName}}</b><br>
                    {{buyer.customerAddress}}<br>
                    {{buyer.country}} {{buyer.zipCode}}
                  </span>
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>

   
    <div class="shipping-details-section">
      <div class="shipping-row">
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Pre-Carriage By</mat-label>
              <input matInput formControlName="preCarriageBy"
                     [matAutocomplete]="autoPreCarriageByRef"
                     (blur)="onPreCarriageByBlur()"
                     (input)="toUppercaseInput($event, 'preCarriageBy')">
              <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
              <mat-autocomplete #autoPreCarriageByRef="matAutocomplete">
                <mat-option *ngFor="let ref of preCarriageByRefs" [value]="ref">
                  <span class="option-flex-center">
                    {{ ref | uppercase }}
                    <button mat-icon-button type="button"
                      (click)="removePreCarriageByRef(ref); $event.stopPropagation();"
                      aria-label="Remove Pre-Carriage By">
                      <mat-icon>close</mat-icon>
                    </button>
                  </span>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>

<style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>

        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Place of Receipt by Pre-Carrier</mat-label>
              <input matInput formControlName="placeOfReceipt"
                     [matAutocomplete]="autoPlaceOfReceiptRef"
                     (blur)="onPlaceOfReceiptBlur()"
                     (input)="toUppercaseInput($event, 'placeOfReceipt')">
                     <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
              <mat-autocomplete #autoPlaceOfReceiptRef="matAutocomplete">
                <mat-option *ngFor="let ref of placeOfReceiptRefs" [value]="ref">
                  <span>{{ ref | uppercase }}</span>
                  <button mat-icon-button type="button" (click)="removePlaceOfReceiptRef(ref); $event.stopPropagation();" aria-label="Remove Place of Receipt" style="float:right; margin-left:8px;">
                    <mat-icon>close</mat-icon>
                  </button>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>
        <style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Country of Origin of Goods</mat-label>
              <input matInput formControlName="originCountry" (input)="toUppercaseInput($event, 'originCountry')">
            </mat-form-field>
          </div>
        </div>
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Country of Final Destination</mat-label>
              <mat-select formControlName="destinationCountry" (selectionChange)="toUppercaseSelect($event.value, 'destinationCountry')">
                <mat-option *ngFor="let country of countries" [value]="country.value">
                  {{country.viewValue}}
                </mat-option>
              </mat-select>

            </mat-form-field>
          </div>
        </div>
      </div>
      <style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>
      <div class="shipping-row">
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Vessel/Flight No.</mat-label>
              <input matInput formControlName="vesselNo"
                     [matAutocomplete]="autoVesselNoRef"
                     (blur)="onVesselNoBlur()"
                     (input)="toUppercaseInput($event, 'vesselNo')">
                     <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
              <mat-autocomplete #autoVesselNoRef="matAutocomplete">
                <mat-option *ngFor="let ref of vesselNoRefs" [value]="ref">
                  <span>{{ ref | uppercase }}</span>
                  <button mat-icon-button type="button" (click)="removeVesselNoRef(ref); $event.stopPropagation();" aria-label="Remove Vessel No" style="float:right; margin-left:8px;">
                    <mat-icon>close</mat-icon>
                  </button>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>
        <style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Port of Loading</mat-label>
              <mat-select formControlName="portOfLoading" (selectionChange)="toUppercaseSelect($event.value, 'portOfLoading')">
                <mat-option *ngFor="let port of indianPorts" [value]="port.value">
                  {{port.viewValue}}
                </mat-option>
              </mat-select>

            </mat-form-field>
          </div>
        </div>
        <style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Port of Discharge</mat-label>
              <input matInput formControlName="portOfDischarge"
                     [matAutocomplete]="autoPortOfDischargeRef"
                     (blur)="onPortOfDischargeBlur()"
                     (input)="toUppercaseInput($event, 'portOfDischarge')">
                     <span matSuffix style="margin-right: 12px;">
                <i class="fa-solid fa-caret-down text-secondary"></i>
              </span>
              <mat-autocomplete #autoPortOfDischargeRef="matAutocomplete">
                <mat-option *ngFor="let ref of portOfDischargeRefs" [value]="ref">
                  <span>{{ ref | uppercase }}</span>
                  <button mat-icon-button type="button" (click)="removePortOfDischargeRef(ref); $event.stopPropagation();" aria-label="Remove Port of Discharge" style="float:right; margin-left:8px;">
                    <mat-icon>close</mat-icon>
                  </button>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>
        <style>
  .option-flex-center {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    width: 100%;
  }
</style>
        <div class="shipping-cell">
          <div class="shipping-label"></div>
          <div class="shipping-value">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Final Destination</mat-label>
              <mat-select formControlName="finalDestination" (selectionChange)="toUppercaseSelect($event.value, 'finalDestination')">
                <mat-option *ngFor="let country of countries" [value]="country.value">
                  {{country.viewValue}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
 <!-- Shipping Details Section -->
    <div class="text-center mt-3 mb-3">
      <button mat-raised-button color="primary" (click)="onSaveShippingDetails()" [disabled]="isSubmitting">
        <mat-icon>arrow_forward</mat-icon> Go to Packing List
      </button>
    </div>


    <!-- Marks and Package Section -->
    <div class="marks-package-section">
      <div class="marks-box">
        <div class="marks-label"></div>
        <div class="marks-value">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>MARKS & CONT NO.</mat-label>
<input matInput formControlName="marksAndNos" style="text-transform:uppercase; text-decoration:underline;" (input)="toUppercaseInput($event, 'marksAndNos')">
<mat-autocomplete #autoMarksAndNosRef="matAutocomplete" (optionSelected)="invoiceForm.get('marksAndNos')?.setValue($event.option.value)">
  <mat-option *ngFor="let ref of marksAndNosRefs" [value]="ref">
    <span>{{ ref | uppercase }}</span>
    <button mat-icon-button type="button" (click)="$event.stopPropagation(); removeMarksAndNosRef(ref)" style="float:right;">
      <mat-icon>close</mat-icon>
    </button>
  </mat-option>
</mat-autocomplete>
          </mat-form-field>
        </div>
      </div>
      <div class="package-box">
        <div class="package-label"></div>
        <div class="package-value">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>No & Kind of Package</mat-label>
            <input matInput formControlName="noOfKindOfPackage"
                   (input)="toUppercaseInput($event, 'noOfKindOfPackage')"
                   [matAutocomplete]="autoNoOfKindOfPackageRef"
                   (blur)="onNoOfKindOfPackageBlur()">
            <mat-autocomplete #autoNoOfKindOfPackageRef="matAutocomplete" (optionSelected)="invoiceForm.get('noOfKindOfPackage')?.setValue($event.option.value)">
              <mat-option *ngFor="let ref of noOfKindOfPackageRefs" [value]="ref">
                <span>{{ ref | uppercase }}</span>
                <button mat-icon-button type="button" (click)="$event.stopPropagation(); removeNoOfKindOfPackageRef(ref)" style="float:right;">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
      </div>
      <div class="description-box">
        <div class="description-label"></div>
        <div class="description-value">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description of goods</mat-label>
            <input matInput formControlName="descriptionOfGoods"
                   [matAutocomplete]="autoDescriptionOfGoodsRef"
                   (blur)="onDescriptionOfGoodsBlur()"
                   (input)="toUppercaseInput($event, 'descriptionOfGoods')">
            <mat-autocomplete #autoDescriptionOfGoodsRef="matAutocomplete">
              <mat-option *ngFor="let ref of descriptionOfGoodsRefs" [value]="ref">
                <span>{{ ref | uppercase }}</span>
                <button mat-icon-button type="button" (click)="removeDescriptionOfGoodsRef(ref); $event.stopPropagation();" aria-label="Remove Description of Goods" style="float:right; margin-left:8px;">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Goods Table Section -->
    <div class="goods-table-container" id="goods-table-section">
      <table class="goods-table" formArrayName="goods">
        <thead>
          <tr>
            <th class="quality-column">QUALITY</th>
            <th class="design-column">DESIGN</th>
            <th class="pieces-column">PIECES</th>
            <th class="quantity-column">QUANTITY (SQ.M)</th>
            <th class="rate-column">RATE FOB (EUR)</th>
            <th class="euro-column">AMOUNT FOB (EUR)</th>
            <!-- <th class="action-column">ACTION</th> -->
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of goods.controls; let i = index" [formGroupName]="i">
            <td class="quality-column">
              <input type="text" formControlName="quality" class="table-input" (input)="toUppercaseInput($event, 'goods.' + i + '.quality')">
            </td>
            <td class="design-column">
              <input type="text" formControlName="design" class="table-input" (input)="toUppercaseInput($event, 'goods.' + i + '.design')">
            </td>
            <td class="pieces-column">
              <input type="number" formControlName="pieces" class="table-input" (input)="calculateTotals()">
            </td>
            <td class="quantity-column">
              <input type="number" formControlName="quantitySqMeter" class="table-input" (input)="calculateTotals()">
            </td>
            <td class="rate-column">
              <input type="number" formControlName="rateFOB" class="table-input" (input)="calculateTotals()">
            </td>
            <td class="euro-column">
              <input type="number" formControlName="amountFOB" readonly class="table-input">
            </td>
            <!-- <td class="action-column">
              <button type="button" class="remove-button" (click)="removeGoodsItem(i)" *ngIf="goods.controls.length > 1">✕</button>
            </td> -->
          </tr>
        </tbody>
      </table>
      <!-- <button type="button" class="add-button" (click)="addGoodsItem()">+ Add Item</button> -->
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
      <table class="summary-table">
        <tr class="total-row">
          <td class="total-label">TOTAL</td>
          <!-- <td class="quality-column"></td> -->
          <td class="design-column"></td>
          <td class="pieces-column">{{ totalPieces }}</td>
          <td class="quantity-column">{{ totalQuantity }}</td>
          <td class="rate-column"></td>
          <td class="euro-column">{{ totalFOB }}</td>
        </tr>
        <tr class="freight-row">
          <td class="freight-label" colspan="5">Added Freight</td>
          <td class="freight-value">
            <input type="number" formControlName="addedFreight" class="freight-input" (input)="calculateTotals()">
          </td>
        </tr>
        <!-- <tr class="freight-row">
          <td class="freight-label" colspan="5">Insurance</td>
          <td class="freight-value">
            <input type="number" formControlName="insurance" class="freight-input" (input)="calculateTotals()">
          </td>
        </tr> -->
        <!-- <tr class="freight-row">
          <td class="freight-label" colspan="5">IGST %</td>
          <td class="freight-value">
            <input type="number" formControlName="igstPercentage" class="freight-input" (input)="calculateTotals()">
          </td>
        </tr>
        <tr class="freight-row">
          <td class="freight-label" colspan="5">IGST Amount</td>
          <td class="freight-value">
            <input type="number" formControlName="igst" readonly class="freight-input">
          </td>
        </tr> -->
        <tr class="grand-total-row">
          <td class="grand-total-label" colspan="5">GRAND TOTAL</td>
          <td class="grand-total-value">{{ grandTotal }}</td>
        </tr>
      </table>

      <div class="composition-row">
        <div class="wool-section">
          <span class="wool-label">WOOL</span>
          <span class="wool-value">80 %</span>
        </div>
        <div class="cotton-section">
          <span class="cotton-label">COTTON</span>
          <span class="cotton-value">20 %</span>
        </div>
        <div class="weight-section">
          <div class="gross-weight">
            <span class="weight-label">Gross Wt.</span>
            <span class="weight-value">1046.000 KGS</span>
          </div>
          <div class="net-weight">
            <span class="weight-label">Net Wt.</span>
            <span class="weight-value">990.000 KGS</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Amount in Words -->
    <div class="amount-words-section">
      <div class="amount-words-label"></div>
      <div class="amount-words-value">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Amount chargeable in words</mat-label>
          <textarea matInput formControlName="amountChargeableWords" rows="2" (input)="toUppercaseInput($event, 'amountChargeableWords')" style="text-decoration: underline;"></textarea>
        </mat-form-field>
      </div>
    </div>

    <!-- Declaration Section -->
    <div class="declaration-section">
      <div class="declaration-box">
        <div class="declaration-label">Declaration:</div>
        <div class="declaration-text">
          We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.
        </div>
      </div>
      <div class="signature-box">
        <div class="signature-label">Signature</div>
        <div class="signature-value">
          <div class="signature-upload-container">
            <div class="signature-preview" *ngIf="signatureImageUrl">
              <img [src]="signatureImageUrl" alt="Signature" class="signature-image">
              <button mat-icon-button color="warn" type="button" (click)="removeSignature()" class="remove-signature-btn">
                <mat-icon>close</mat-icon>
              </button>
            </div>
            <button mat-raised-button type="button" color="primary" (click)="signatureFileInput.click()" *ngIf="!signatureImageUrl">
              <mat-icon>upload</mat-icon> Upload Signature
            </button>
            <div class="signature-size-hint" *ngIf="!signatureImageUrl">
              <small>Image size must be between 10KB and 100KB</small>
            </div>
            <input type="file" #signatureFileInput style="display: none" accept="image/*" (change)="onSignatureSelected($event)">
          </div>
        </div>
      </div>

    </div>

    <!-- Submit Button -->
    <div class="text-center">
      <!-- Debug Info -->
      <div *ngIf="invoiceForm.invalid" style="margin-bottom: 10px; color: red; font-size: 12px;">
        Form is invalid. Check console for details.
        <button mat-button type="button" (click)="debugFormValidation()" style="margin-left: 10px;">Debug Form</button>
      </div>

      <button mat-raised-button color="primary" type="submit" [disabled]="isSubmitting" *ngIf="!isUpdateMode">
        <span *ngIf="isSubmitting">Saving...</span>
        <span *ngIf="!isSubmitting">Save Invoice</span>
      </button>
      <button mat-raised-button color="accent" type="button" (click)="updateInvoice()" [disabled]="isSubmitting" *ngIf="isUpdateMode">
        <span *ngIf="isSubmitting">Updating...</span>
        <span *ngIf="!isSubmitting">Update Invoice</span>
      </button>
    </div>
  </form>
</mat-card>
